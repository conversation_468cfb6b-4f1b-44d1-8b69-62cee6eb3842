import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Target, TrendingUp, Calendar, BookOpen, DollarSign, Trophy, X, CreditCard as Edit3 } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function Goals() {
  const [showAddGoal, setShowAddGoal] = useState(false);
  const [showJournal, setShowJournal] = useState(false);
  const [goalTitle, setGoalTitle] = useState('');
  const [goalTarget, setGoalTarget] = useState('');
  const [journalEntry, setJournalEntry] = useState('');
  const [selectedGoalType, setSelectedGoalType] = useState('');

  const goals = [
    {
      id: 1,
      title: 'Save $2,000 this month',
      category: 'Financial',
      target: 2000,
      current: 1400,
      unit: '$',
      deadline: '2024-12-31',
      icon: DollarSign,
      color: '#10B981',
    },
    {
      id: 2,
      title: 'Read 12 books this year',
      category: 'Learning',
      target: 12,
      current: 8,
      unit: 'books',
      deadline: '2024-12-31',
      icon: BookOpen,
      color: '#3B82F6',
    },
    {
      id: 3,
      title: 'Complete 100 workouts',
      category: 'Health',
      target: 100,
      current: 67,
      unit: 'workouts',
      deadline: '2024-12-31',
      icon: Trophy,
      color: '#F59E0B',
    },
  ];

  const journalEntries = [
    {
      id: 1,
      date: 'Today',
      entry: 'Made great progress on my savings goal today. Cooked at home instead of ordering takeout.',
      mood: 'positive',
    },
    {
      id: 2,
      date: 'Yesterday',
      entry: 'Finished another book! Really enjoyed "Atomic Habits" - lots of practical insights.',
      mood: 'positive',
    },
    {
      id: 3,
      date: '2 days ago',
      entry: 'Skipped the gym today due to work stress. Need to find better balance.',
      mood: 'neutral',
    },
  ];

  const goalTypes = [
    { name: 'Financial', icon: DollarSign, color: '#10B981' },
    { name: 'Learning', icon: BookOpen, color: '#3B82F6' },
    { name: 'Health', icon: Trophy, color: '#F59E0B' },
    { name: 'Career', icon: TrendingUp, color: '#8B5CF6' },
  ];

  const calculateProgress = (current, target) => {
    return Math.min((current / target) * 100, 100);
  };

  const GoalCard = ({ goal }) => {
    const IconComponent = goal.icon;
    const progress = calculateProgress(goal.current, goal.target);
    
    return (
      <View style={styles.goalCard}>
        <View style={styles.goalHeader}>
          <View style={[styles.goalIcon, { backgroundColor: goal.color }]}>
            <IconComponent color="#FFFFFF" size={20} />
          </View>
          <View style={styles.goalInfo}>
            <Text style={styles.goalTitle}>{goal.title}</Text>
            <Text style={styles.goalCategory}>{goal.category}</Text>
          </View>
        </View>
        
        <View style={styles.goalProgress}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              {goal.current} / {goal.target} {goal.unit}
            </Text>
            <Text style={styles.progressPercent}>{Math.round(progress)}%</Text>
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${progress}%`, backgroundColor: goal.color }]} />
          </View>
        </View>
        
        <View style={styles.goalFooter}>
          <Text style={styles.goalDeadline}>Due: {goal.deadline}</Text>
          <TouchableOpacity style={styles.updateButton}>
            <Edit3 color="#6B7280" size={16} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const JournalEntry = ({ entry }) => (
    <View style={styles.journalEntry}>
      <View style={styles.journalHeader}>
        <Text style={styles.journalDate}>{entry.date}</Text>
        <View style={[styles.moodIndicator, { backgroundColor: entry.mood === 'positive' ? '#10B981' : '#F59E0B' }]} />
      </View>
      <Text style={styles.journalText}>{entry.entry}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Goals</Text>
            <Text style={styles.subtitle}>Track your progress</Text>
          </View>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={() => setShowAddGoal(true)}
          >
            <Plus color="#FFFFFF" size={24} />
          </TouchableOpacity>
        </View>

        {/* Overview Stats */}
        <View style={styles.overviewContainer}>
          <View style={styles.overviewCard}>
            <Text style={styles.overviewValue}>3</Text>
            <Text style={styles.overviewLabel}>Active Goals</Text>
          </View>
          <View style={styles.overviewCard}>
            <Text style={styles.overviewValue}>67%</Text>
            <Text style={styles.overviewLabel}>Avg Progress</Text>
          </View>
          <View style={styles.overviewCard}>
            <Text style={styles.overviewValue}>1</Text>
            <Text style={styles.overviewLabel}>Completed</Text>
          </View>
        </View>

        {/* Active Goals */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Active Goals</Text>
          
          {goals.map((goal) => (
            <GoalCard key={goal.id} goal={goal} />
          ))}
        </View>

        {/* Journal Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Journal</Text>
            <TouchableOpacity 
              style={styles.journalButton}
              onPress={() => setShowJournal(true)}
            >
              <Edit3 color="#2563EB" size={20} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.journalContainer}>
            {journalEntries.map((entry) => (
              <JournalEntry key={entry.id} entry={entry} />
            ))}
          </View>
        </View>

        {/* Monthly Review */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>This Month's Insights</Text>
          
          <View style={styles.insightsContainer}>
            <View style={styles.insightCard}>
              <TrendingUp color="#10B981" size={24} />
              <Text style={styles.insightTitle}>Great Progress!</Text>
              <Text style={styles.insightText}>
                You're 15% ahead of schedule on your savings goal
              </Text>
            </View>
            
            <View style={styles.insightCard}>
              <Target color="#3B82F6" size={24} />
              <Text style={styles.insightTitle}>Stay Consistent</Text>
              <Text style={styles.insightText}>
                2 more books to reach your reading target
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Add Goal Modal */}
      <Modal
        visible={showAddGoal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>New Goal</Text>
            <TouchableOpacity onPress={() => setShowAddGoal(false)}>
              <X color="#6B7280" size={24} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Goal Title</Text>
              <TextInput
                style={styles.formInput}
                value={goalTitle}
                onChangeText={setGoalTitle}
                placeholder="What do you want to achieve?"
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Target</Text>
              <TextInput
                style={styles.formInput}
                value={goalTarget}
                onChangeText={setGoalTarget}
                placeholder="Enter your target (number)"
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Category</Text>
              <View style={styles.categorySelector}>
                {goalTypes.map((type, index) => {
                  const IconComponent = type.icon;
                  return (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.categoryOption,
                        selectedGoalType === type.name && styles.categoryOptionSelected
                      ]}
                      onPress={() => setSelectedGoalType(type.name)}
                    >
                      <View style={[styles.categoryOptionIcon, { backgroundColor: type.color }]}>
                        <IconComponent color="#FFFFFF" size={20} />
                      </View>
                      <Text style={styles.categoryOptionText}>{type.name}</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
            
            <TouchableOpacity style={styles.saveButton}>
              <Text style={styles.saveButtonText}>Create Goal</Text>
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Journal Modal */}
      <Modal
        visible={showJournal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Journal Entry</Text>
            <TouchableOpacity onPress={() => setShowJournal(false)}>
              <X color="#6B7280" size={24} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>How did your day go?</Text>
              <TextInput
                style={[styles.formInput, { height: 150 }]}
                value={journalEntry}
                onChangeText={setJournalEntry}
                placeholder="Write about your progress, challenges, or thoughts..."
                multiline
                textAlignVertical="top"
              />
            </View>
            
            <TouchableOpacity style={styles.saveButton}>
              <Text style={styles.saveButtonText}>Save Entry</Text>
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#2563EB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  overviewContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    gap: 16,
    marginBottom: 24,
  },
  overviewCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  overviewValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  journalButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#EFF6FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  goalCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  goalIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  goalCategory: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  goalProgress: {
    marginBottom: 16,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  progressPercent: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#2563EB',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  goalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalDeadline: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  updateButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  journalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  journalEntry: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  journalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  journalDate: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  moodIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  journalText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
    lineHeight: 24,
  },
  insightsContainer: {
    gap: 12,
  },
  insightCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginLeft: 12,
    marginBottom: 4,
  },
  insightText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 12,
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  formGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    backgroundColor: '#FFFFFF',
  },
  categorySelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    width: (width - 80) / 2,
  },
  categoryOptionSelected: {
    borderColor: '#2563EB',
    backgroundColor: '#EFF6FF',
  },
  categoryOptionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  categoryOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  saveButton: {
    backgroundColor: '#2563EB',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  saveButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});